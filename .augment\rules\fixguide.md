---
type: "manual"
---

# MDX编译错误修复指南

## 常见错误类型及修复方法

### 1. 数学公式解析错误

**错误信息：**
```
Cannot set properties of undefined (setting 'value')
at Object.exitMathText (mdast-util-math/index.js:111:14)
```

**原因：** MDX解析器将文档中的`$`符号误认为是数学公式语法标记

**修复方法：**
- 将所有美元符号`$`转义为`\$`
- 例如：`$40` → `\$40`
- 例如：`$1.008 per month ($0.144/per kWh)` → `\$1.008 per month (\$0.144/per kWh)`

### 2. 箭头符号解析错误

**错误信息：**
```
Failed to generate code because ast or source is not set
Unexpected character `<` (U+003C) before name, expected a character that can start a name
```

**原因：** MDX解析器将箭头符号误认为HTML标签的开始或结束标记

**修复方法：**
- 将所有箭头符号替换为HTML实体或Unicode符号
- 修复对照表：
  - `->` → `→` (Unicode右箭头)
  - `<-` → `←` (Unicode左箭头)  
  - `>` → `&gt;` (HTML实体) 或 `＞` (全角符号)
  - `<` → `&lt;` (HTML实体) 或 `＜` (全角符号)
  - `>=` → `≥` (Unicode大于等于)
  - `<=` → `≤` (Unicode小于等于)

**示例修复：**
```markdown
<!-- 错误 -->
配置文件 -> 设置选项 -> 保存
if (value > 10) { ... }
temperature < 25°C

<!-- 正确 -->
配置文件 → 设置选项 → 保存  
if (value &gt; 10) { ... }
temperature &lt; 25°C
```

**注意事项：**
- 在代码块（```）内的箭头符号通常不需要转义
- 要小心不要误替换HTML标签中的 `<` 和 `>`
- 建议使用Unicode符号而不是HTML实体，因为更易读

### 3. HTML标签结构错误

**错误信息：**
```
Expected a closing tag for `<td>` (39:4-39:8) before the end of `paragraph`
```

**原因：** HTML标签格式不正确，特别是表格中的`<td>`标签内容跨越多行且缩进不一致

**修复方法：**
- 重新格式化HTML标签结构
- 确保所有`<td>`标签内容使用统一缩进
- 将标签开始和结束放在同一个逻辑块中
- 示例修复：
```html
<!-- 错误格式 -->
<td><div style={{textAlign: 'left'}}><a href="...">链接1</a></div>
              <div style={{textAlign: 'left'}}><a href="...">链接2</a></div>
      </td>

<!-- 正确格式 -->
<td>
  <div style={{textAlign: 'left'}}><a href="...">链接1</a></div>
  <div style={{textAlign: 'left'}}><a href="...">链接2</a></div>
</td>
```

### 4. 特殊字符转义问题

**原因：** 某些特殊字符被MDX解析器误解

**修复方法：**
- 将`\*`符号改为`×`符号（乘号）
- 例如：`154\*100\*44 mm` → `154×100×44 mm`
- 避免使用可能被误解为转义字符的符号

### 5. 标题格式问题

**错误信息：**
```
Failed to generate code because ast or source is not set
```

**原因：** 非标准的标题格式导致解析器混淆

**修复方法：**
- 使用标准的Markdown标题格式
- 错误：
```markdown
FAQ
===
```
- 正确：
```markdown
# FAQ
```

## 自动化修复规则

### 修复优先级

**高优先级（必须修复）：**
1. 美元符号 `$` → `\$`
2. 箭头符号 `->` `<-` `>` `<` → Unicode或HTML实体
3. HTML标签结构问题

**中优先级（建议修复）：**
4. 复杂表格 → Markdown表格
5. 特殊字符 `\*` → `×`

**低优先级（可选）：**
6. 标题格式标准化

### 正则表达式规则

1. **美元符号转义**
   - 查找：`\$([0-9]+\.?[0-9]*)`
   - 替换：`\\$\$1`

2. **箭头符号替换**
   - 查找：`->`
   - 替换：`→`
   - 查找：`<-`
   - 替换：`←`
   - 查找：`(?<![=!><])<(?![=/])`
   - 替换：`&lt;`
   - 查找：`(?<![=!><])>(?!=)`
   - 替换：`&gt;`

3. **乘号符号替换**
   - 查找：`([0-9]+)\\?\*([0-9]+)\\?\*([0-9]+)`
   - 替换：`$1×$2×$3`

4. **标题格式标准化**
   - 查找：`^([A-Z][A-Za-z\s]+)\n={3,}$`
   - 替换：`# $1`

### 检查清单

在修复MDX文档时，按以下顺序检查：

1. ✅ **美元符号检查**：搜索所有`$`并转义为`\$`
2. ✅ **箭头符号检查**：搜索`->`、`<-`、`>`、`<`并替换为安全符号
3. ✅ **HTML标签检查**：确保所有HTML标签正确嵌套和缩进
4. ✅ **表格格式检查**：将复杂HTML表格转换为Markdown表格
5. ✅ **特殊字符检查**：处理`\*`、转义字符等
6. ✅ **标题格式检查**：使用标准Markdown标题格式
7. ✅ **整体格式检查**：确保文档结构清晰统一

## 测试验证

修复后务必进行以下验证：
1. 运行`yarn build`确认无编译错误
2. 检查生成的页面显示是否正常
3. 验证所有链接和图片是否正常显示
