{"name": "my-website", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "cross-env NODE_OPTIONS=--max-old-space-size=4096 docusaurus start --host 0.0.0.0", "build": "cross-env NODE_OPTIONS=--max-old-space-size=8192 docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "install:frozen": "yarn install --frozen-lockfile", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc", "generatewiki": "node scripts/generateWiki.js", "generate-lang-map": "node scripts/generate-language-map.js", "migrate-mdx": "node scripts/start-migration.js", "migrate-mdx-dry-run": "node scripts/mdx-migration-assistant.js --dry-run"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@docusaurus/core": "^3.8.1", "@docusaurus/faster": "^3.8.1", "@docusaurus/plugin-content-docs": "^3.8.1", "@docusaurus/plugin-debug": "^3.8.1", "@docusaurus/preset-classic": "^3.8.1", "@docusaurus/theme-live-codeblock": "^2.4.3", "@giscus/react": "^2.2.4", "@mdx-js/react": "^3.1.0", "antd": "^5.19.3", "chalk": "^5.4.1", "clsx": "^1.2.1", "csv-parser": "^3.2.0", "docusaurus-plugin-image-zoom": "^0.1.4", "docusaurus-plugin-sass": "^0.2.2", "docusaurus-theme-search-typesense": "^0.16.0-0", "moment": "^2.30.1", "prism-react-renderer": "^1.3.5", "rc-progress": "^4.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-icons": "^5.3.0", "react-popper": "^2.3.0", "react-toastify": "^10.0.6", "rehype-katex": "^6.0.0", "remark-math": "5.0.0", "sass": "^1.89.1", "swiper": "^11.1.0"}, "devDependencies": {"@docusaurus/eslint-plugin": "^2.4.3", "@docusaurus/module-type-aliases": "^2.4.3", "@tsconfig/docusaurus": "^1.0.5", "cross-env": "^7.0.3", "typescript": "^4.7.4"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18"}}